@extends('layouts.user')

@section('title', 'Dashboard')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white" data-aos="fade-up">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold mb-2">Welcome back, {{ auth()->user()->name }}! 👋</h1>
                <p class="text-blue-100 text-lg">Discover amazing events and manage your tickets</p>
                <p class="text-sm text-blue-200 mt-2">{{ now()->format('l, d F Y') }}</p>
            </div>
            <div class="hidden md:block">
                <div class="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                    <i data-lucide="calendar-heart" class="w-12 h-12 text-white"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="100">
        <!-- Total Orders -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Orders</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['total_orders'] ?? 0 }}</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="shopping-bag" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                </div>
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Upcoming Events</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['upcoming_events'] ?? 0 }}</p>
                </div>
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="calendar" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                </div>
            </div>
        </div>

        <!-- UangTix Balance -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">UangTix Balance</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">Rp {{ number_format($stats['uangtix_balance'] ?? 0, 0, ',', '.') }}</p>
                </div>
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="coins" class="w-6 h-6 text-yellow-600 dark:text-yellow-400"></i>
                </div>
            </div>
        </div>

        <!-- Total Spent -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover-lift">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Spent</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">Rp {{ number_format($stats['total_spent'] ?? 0, 0, ',', '.') }}</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                    <i data-lucide="credit-card" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6" data-aos="fade-up" data-aos-delay="200">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">Quick Actions</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{{ route('events.index') }}" class="group flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-900/40 transition-colors duration-200">
                    <i data-lucide="search" class="w-6 h-6 text-blue-600 dark:text-blue-400"></i>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Browse Events</span>
            </a>

            <a href="{{ route('orders.index') }}" class="group flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center group-hover:bg-green-200 dark:group-hover:bg-green-900/40 transition-colors duration-200">
                    <i data-lucide="ticket" class="w-6 h-6 text-green-600 dark:text-green-400"></i>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2 group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">My Tickets</span>
            </a>

            <a href="{{ route('uangtix.index') }}" class="group flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg flex items-center justify-center group-hover:bg-yellow-200 dark:group-hover:bg-yellow-900/40 transition-colors duration-200">
                    <i data-lucide="wallet" class="w-6 h-6 text-yellow-600 dark:text-yellow-400"></i>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2 group-hover:text-yellow-600 dark:group-hover:text-yellow-400 transition-colors">Top Up UangTix</span>
            </a>

            <a href="{{ route('profile.edit') }}" class="group flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center group-hover:bg-purple-200 dark:group-hover:bg-purple-900/40 transition-colors duration-200">
                    <i data-lucide="user" class="w-6 h-6 text-purple-600 dark:text-purple-400"></i>
                </div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mt-2 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">Edit Profile</span>
            </a>
        </div>
    </div>

    <!-- Recent Activity & Upcoming Events -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8" data-aos="fade-up" data-aos-delay="300">
        <!-- Recent Orders -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Orders</h3>
                <a href="{{ route('orders.index') }}" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">View All</a>
            </div>
            <div class="space-y-4">
                @if(isset($recentOrders) && count($recentOrders) > 0)
                    @foreach($recentOrders->take(3) as $order)
                    <div class="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="ticket" class="w-5 h-5 text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $order->ticket->title ?? 'Event' }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $order->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900 dark:text-white">Rp {{ number_format($order->total_amount, 0, ',', '.') }}</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                {{ $order->status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : 
                                   ($order->status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' : 
                                   'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400') }}">
                                {{ ucfirst($order->status) }}
                            </span>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-8">
                        <i data-lucide="shopping-bag" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                        <p class="text-gray-600 dark:text-gray-400">No orders yet</p>
                        <a href="{{ route('events.index') }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">Browse events to get started</a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Upcoming Events -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Upcoming Events</h3>
                <a href="{{ route('events.index') }}" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">View All</a>
            </div>
            <div class="space-y-4">
                @if(isset($upcomingEvents) && count($upcomingEvents) > 0)
                    @foreach($upcomingEvents->take(3) as $event)
                    <div class="flex items-center justify-between p-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors duration-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                                <i data-lucide="calendar" class="w-5 h-5 text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ $event->title }}</p>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ $event->event_date->format('M d, Y') }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $event->location }}</p>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $event->event_date->format('H:i') }}</p>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="text-center py-8">
                        <i data-lucide="calendar" class="w-12 h-12 mx-auto mb-4 text-gray-400"></i>
                        <p class="text-gray-600 dark:text-gray-400">No upcoming events</p>
                        <a href="{{ route('events.index') }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm">Discover events</a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Recommended Events -->
    @if(isset($recommendedEvents) && count($recommendedEvents) > 0)
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6" data-aos="fade-up" data-aos-delay="400">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Recommended for You</h3>
            <a href="{{ route('events.index') }}" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">View All</a>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            @foreach($recommendedEvents->take(3) as $event)
            <div class="group cursor-pointer">
                <div class="aspect-w-16 aspect-h-9 mb-4">
                    <img src="{{ $event->image_url }}" alt="{{ $event->title }}" class="w-full h-48 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300">
                </div>
                <h4 class="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">{{ $event->title }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $event->event_date->format('M d, Y') }} • {{ $event->location }}</p>
                <p class="text-lg font-bold text-blue-600 dark:text-blue-400 mt-2">Rp {{ number_format($event->price, 0, ',', '.') }}</p>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection
