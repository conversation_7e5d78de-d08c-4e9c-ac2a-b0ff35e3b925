@extends('layouts.app')

@section('title', 'UangTix - Dompet Digital')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-coins text-warning me-2"></i>
                UangTix
            </h1>
            <p class="text-muted">Dompet digital untuk transaksi di TikPro</p>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#depositModal">
                <i class="fas fa-plus"></i> Deposit
            </button>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#withdrawModal">
                <i class="fas fa-minus"></i> Tarik
            </button>
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#transferModal">
                <i class="fas fa-exchange-alt"></i> Transfer
            </button>
        </div>
    </div>

    <!-- Balance Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-warning text-white shadow-lg">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h2 class="text-white mb-1">{{ $balance->formatted_balance }}</h2>
                            <p class="text-white-75 mb-0">Saldo UangTix Anda</p>
                            <small class="text-white-50">
                                Setara dengan {{ $balance->formatted_balance_idr }}
                            </small>
                        </div>
                        <div class="col-4 text-end">
                            <i class="fas fa-wallet fa-4x text-white-25"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Earned
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                UTX {{ number_format($stats['total_earned'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Total Spent
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                UTX {{ number_format($stats['total_spent'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Transaksi Bulan Ini
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['transactions_this_month'], 0, ',', '.') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Status Akun
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                <span class="badge badge-{{ $balance->is_active ? 'success' : 'danger' }}">
                                    {{ $balance->is_active ? 'Aktif' : 'Nonaktif' }}
                                </span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Exchange Rate Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-left-warning shadow">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <h6 class="text-warning font-weight-bold">Kurs Saat Ini</h6>
                            <div class="small">
                                <div>{{ $exchangeRate->formatted_rates['idr_to_uangtix'] }}</div>
                                <div>{{ $exchangeRate->formatted_rates['uangtix_to_idr'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-info font-weight-bold">Batas Transaksi</h6>
                            <div class="small">
                                <div>Min Deposit: {{ $exchangeRate->formatted_limits['min_deposit'] }}</div>
                                <div>Max Deposit: {{ $exchangeRate->formatted_limits['max_deposit'] }}</div>
                                <div>Min Penarikan: {{ $exchangeRate->formatted_limits['min_withdrawal'] }}</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-success font-weight-bold">Biaya Transaksi</h6>
                            <div class="small">
                                <div>Biaya Deposit: {{ $exchangeRate->formatted_fees['deposit_fee'] }}</div>
                                <div>Biaya Penarikan: {{ $exchangeRate->formatted_fees['withdrawal_fee'] }}</div>
                                <div>Biaya Transfer: 0%</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h6 class="text-primary font-weight-bold">Status Layanan</h6>
                            <div class="small">
                                <div>
                                    <span class="badge badge-{{ $exchangeRate->deposits_enabled ? 'success' : 'danger' }}">
                                        Deposit {{ $exchangeRate->deposits_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-{{ $exchangeRate->withdrawals_enabled ? 'success' : 'danger' }}">
                                        Penarikan {{ $exchangeRate->withdrawals_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                                <div class="mt-1">
                                    <span class="badge badge-{{ $exchangeRate->transfers_enabled ? 'success' : 'danger' }}">
                                        Transfer {{ $exchangeRate->transfers_enabled ? 'Aktif' : 'Nonaktif' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Requests -->
    @if($pendingRequests->count() > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-clock me-2"></i>
                        Permintaan Pending
                    </h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>No. Permintaan</th>
                                    <th>Jenis</th>
                                    <th>Jumlah</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($pendingRequests as $request)
                                <tr>
                                    <td>{{ $request->request_number }}</td>
                                    <td>
                                        <span class="badge badge-{{ $request->type == 'deposit' ? 'success' : 'warning' }}">
                                            {{ $request->type_label }}
                                        </span>
                                    </td>
                                    <td>{{ $request->formatted_amount }}</td>
                                    <td>
                                        <span class="badge badge-{{ $request->status_color }}">
                                            {{ $request->status_label }}
                                        </span>
                                    </td>
                                    <td>{{ $request->created_at->format('d/m/Y H:i') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Recent Transactions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        Transaksi Terbaru
                    </h6>
                    <a href="{{ route('uangtix.transactions') }}" class="btn btn-sm btn-outline-primary">
                        Lihat Semua
                    </a>
                </div>
                <div class="card-body">
                    @if($recentTransactions->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Tanggal</th>
                                        <th>Jenis</th>
                                        <th>Deskripsi</th>
                                        <th>Jumlah</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentTransactions as $transaction)
                                    <tr>
                                        <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
                                        <td>
                                            <span class="badge badge-{{ $transaction->type_color }}">
                                                <i class="{{ $transaction->type_icon }} me-1"></i>
                                                {{ $transaction->type_label }}
                                            </span>
                                        </td>
                                        <td>{{ $transaction->description }}</td>
                                        <td>
                                            <span class="font-weight-bold text-{{ $transaction->amount > 0 ? 'success' : 'danger' }}">
                                                {{ $transaction->formatted_amount }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ $transaction->status_color }}">
                                                {{ $transaction->status_label }}
                                            </span>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-gray-300 mb-3"></i>
                            <p class="text-muted">Belum ada transaksi UangTix</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Deposit Modal -->
<div class="modal fade" id="depositModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus text-success me-2"></i>
                    Deposit UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="depositForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="depositAmount" class="form-label">Jumlah Deposit (IDR)</label>
                        <input type="number" class="form-control" id="depositAmount" name="amount_idr"
                               min="10000" max="10000000" required>
                        <div class="form-text">
                            Minimum: Rp 10,000 | Maksimum: Rp 10,000,000
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">Metode Pembayaran</label>
                        <select class="form-control" id="paymentMethod" name="payment_method" required>
                            <option value="">Pilih metode pembayaran</option>
                            <option value="bank_transfer">Transfer Bank</option>
                            <option value="e_wallet">E-Wallet</option>
                            <option value="virtual_account">Virtual Account</option>
                        </select>
                    </div>
                    <div id="depositCalculation" class="alert alert-info" style="display: none;">
                        <h6>Rincian Deposit:</h6>
                        <div id="calculationDetails"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Buat Permintaan Deposit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div class="modal fade" id="withdrawModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-minus text-warning me-2"></i>
                    Tarik UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="withdrawForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="withdrawAmount" class="form-label">Jumlah Penarikan (UTX)</label>
                        <input type="number" class="form-control" id="withdrawAmount" name="amount_uangtix"
                               min="10" max="{{ $balance->balance }}" required>
                        <div class="form-text">
                            Saldo tersedia: {{ $balance->formatted_balance }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="bankName" class="form-label">Nama Bank</label>
                        <input type="text" class="form-control" id="bankName" name="bank_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="bankAccountNumber" class="form-label">Nomor Rekening</label>
                        <input type="text" class="form-control" id="bankAccountNumber" name="bank_account_number" required>
                    </div>
                    <div class="mb-3">
                        <label for="bankAccountName" class="form-label">Nama Pemilik Rekening</label>
                        <input type="text" class="form-control" id="bankAccountName" name="bank_account_name" required>
                    </div>
                    <div id="withdrawCalculation" class="alert alert-warning" style="display: none;">
                        <h6>Rincian Penarikan:</h6>
                        <div id="withdrawCalculationDetails"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">Buat Permintaan Penarikan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-exchange-alt text-info me-2"></i>
                    Transfer UangTix
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="transferForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="toUserEmail" class="form-label">Email Penerima</label>
                        <input type="email" class="form-control" id="toUserEmail" name="to_user_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="transferAmount" class="form-label">Jumlah Transfer (UTX)</label>
                        <input type="number" class="form-control" id="transferAmount" name="amount"
                               min="1" max="{{ $balance->balance }}" required>
                        <div class="form-text">
                            Saldo tersedia: {{ $balance->formatted_balance }}
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="transferDescription" class="form-label">Keterangan (Opsional)</label>
                        <textarea class="form-control" id="transferDescription" name="description"
                                  rows="3" placeholder="Keterangan transfer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-info">Transfer UangTix</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Deposit form handling
document.getElementById('depositForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    try {
        const response = await fetch('/uangtix/deposit', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('depositModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

// Withdraw form handling
document.getElementById('withdrawForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    try {
        const response = await fetch('/uangtix/withdraw', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('withdrawModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

// Transfer form handling
document.getElementById('transferForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    try {
        const response = await fetch('/uangtix/transfer', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: formData
        });

        const data = await response.json();

        if (data.success) {
            showAlert('success', data.message);
            bootstrap.Modal.getInstance(document.getElementById('transferModal')).hide();
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert('error', data.message);
        }
    } catch (error) {
        showAlert('error', 'Terjadi kesalahan jaringan');
    }
});

function showAlert(type, message) {
    // You can implement your preferred alert system here
    alert(message);
}
</script>
@endpush
