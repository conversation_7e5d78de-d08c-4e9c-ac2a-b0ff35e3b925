@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-primary/5 via-white to-secondary/5">

    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8" data-aos="fade-up">
                <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Selesaikan Pembayaran
                </h1>
                <p class="text-lg text-gray-600 mb-2">
                    Silakan lakukan pembayaran sesuai instruksi di bawah ini
                </p>
                <p class="text-sm text-gray-500">
                    Order #{{ $order->order_number }} • Berlaku hingga:
                    <span id="countdown" class="font-semibold text-orange-600"></span>
                </p>
            </div>
        </div>
    </section>

    <!-- Payment Details -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Payment Instructions -->
                <div class="lg:col-span-2 space-y-6">

                    @if($order->payment_method === 'qris' && isset($paymentData['qr_string']))
                        <!-- QRIS Payment -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                                </svg>
                                Scan QR Code QRIS
                            </h2>

                            <div class="text-center">
                                <div class="w-64 h-64 mx-auto bg-white border-2 border-gray-200 rounded-lg p-4 mb-6">
                                    <div id="qrcode"></div>
                                </div>

                                <div class="space-y-3 text-sm text-gray-700">
                                    <p class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Scan dengan aplikasi bank atau e-wallet apapun
                                    </p>
                                    <p class="flex items-center justify-center">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Pembayaran akan dikonfirmasi otomatis
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($order->payment_method === 'virtual_account' && isset($paymentData['account_number']))
                        <!-- Virtual Account Payment -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                </svg>
                                Virtual Account {{ strtoupper($paymentData['bank_code']) }}
                            </h2>

                            <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                                <div class="text-center mb-6">
                                    <div class="text-sm text-blue-700 mb-2">Nomor Virtual Account</div>
                                    <div class="text-2xl font-mono font-bold text-blue-900 mb-4">
                                        {{ $paymentData['account_number'] }}
                                    </div>
                                    <button onclick="copyToClipboard('{{ $paymentData['account_number'] }}')"
                                            class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                        </svg>
                                        Salin Nomor
                                    </button>
                                </div>

                                <div class="space-y-3 text-sm text-blue-800">
                                    <h4 class="font-semibold">Cara Pembayaran:</h4>
                                    <ol class="list-decimal list-inside space-y-2">
                                        <li>Buka aplikasi mobile banking atau kunjungi ATM</li>
                                        <li>Pilih menu Transfer atau Bayar</li>
                                        <li>Masukkan nomor virtual account di atas</li>
                                        <li>Masukkan nominal <strong>Rp {{ number_format($order->total_amount, 0, ',', '.') }}</strong></li>
                                        <li>Konfirmasi pembayaran</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($order->payment_method === 'e_wallet' && isset($paymentData['payment_url']))
                        <!-- E-Wallet Payment -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                </svg>
                                Pembayaran E-Wallet
                            </h2>

                            <div class="text-center">
                                <div class="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
                                    <p class="text-green-800 mb-4">Klik tombol di bawah untuk melanjutkan pembayaran</p>
                                    <a href="{{ $paymentData['payment_url'] }}"
                                       class="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                        </svg>
                                        Bayar Sekarang
                                    </a>
                                </div>

                                @if(isset($paymentData['qr_code']))
                                    <div class="border-t border-gray-200 pt-6">
                                        <p class="text-gray-600 mb-4">Atau scan QR Code di bawah ini:</p>
                                        <div class="w-48 h-48 mx-auto bg-white border-2 border-gray-200 rounded-lg p-4">
                                            <div id="ewallet-qrcode"></div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    @if($order->payment_method === 'bank_transfer' && isset($paymentData['payment_url']))
                        <!-- Bank Transfer via Tripay -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                                Transfer Bank
                            </h2>

                            <div class="text-center">
                                <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-6">
                                    <p class="text-blue-800 mb-4">Klik tombol di bawah untuk melihat detail pembayaran</p>
                                    <a href="{{ $paymentData['payment_url'] }}"
                                       class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                        </svg>
                                        Lihat Detail Pembayaran
                                    </a>
                                </div>

                                @if(isset($paymentData['virtual_account']))
                                    <div class="border-t border-gray-200 pt-6">
                                        <div class="text-sm text-gray-600 mb-2">Nomor Virtual Account</div>
                                        <div class="text-xl font-mono font-bold text-gray-900 mb-4">
                                            {{ $paymentData['virtual_account'] }}
                                        </div>
                                        <button onclick="copyToClipboard('{{ $paymentData['virtual_account'] }}')"
                                                class="text-blue-600 hover:text-blue-700 text-sm">
                                            Salin Nomor
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- Payment Status Check -->
                    <div class="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-6" data-aos="fade-up" data-aos-delay="100">
                        <h3 class="text-lg font-bold text-yellow-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Status Pembayaran
                        </h3>
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-yellow-800">Menunggu pembayaran...</p>
                                <p class="text-sm text-yellow-700">Halaman akan otomatis refresh setelah pembayaran berhasil</p>
                            </div>
                            <button onclick="checkPaymentStatus()"
                                    class="bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-yellow-700 transition-colors">
                                Cek Status
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    <div class="sticky top-24">
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                            <h3 class="text-lg font-bold text-gray-900 mb-4">Ringkasan Pesanan</h3>

                            <!-- Event Info -->
                            <div class="mb-6">
                                <h4 class="font-semibold text-gray-900 mb-2">{{ $order->event->title }}</h4>
                                <div class="text-sm text-gray-600 space-y-1">
                                    <p>📅 {{ $order->event->start_date->format('d M Y, H:i') }} WIB</p>
                                    <p>📍 {{ $order->event->venue_name }}, {{ $order->event->city }}</p>
                                    <p>👤 {{ $order->customer_name }}</p>
                                    <p>🎫 {{ $order->quantity }} tiket</p>
                                </div>
                            </div>

                            <!-- Payment Summary -->
                            <div class="space-y-3 mb-6">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Subtotal</span>
                                    <span>Rp {{ number_format($order->subtotal, 0, ',', '.') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Biaya admin</span>
                                    <span>Rp {{ number_format($order->admin_fee, 0, ',', '.') }}</span>
                                </div>
                                @if($order->discount_amount > 0)
                                    <div class="flex justify-between text-green-600">
                                        <span>Diskon</span>
                                        <span>-Rp {{ number_format($order->discount_amount, 0, ',', '.') }}</span>
                                    </div>
                                @endif
                                <div class="border-t border-gray-200 pt-3">
                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-gray-900">Total</span>
                                        <span class="text-lg font-bold text-primary">
                                            Rp {{ number_format($order->total_amount, 0, ',', '.') }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Method -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <div class="text-sm text-gray-600 mb-1">Metode Pembayaran</div>
                                <div class="font-semibold text-gray-900">
                                    {{ ucfirst(str_replace('_', ' ', $order->payment_method)) }}
                                </div>
                            </div>

                            <!-- Important Notes -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h4 class="font-semibold text-blue-900 mb-2">Penting!</h4>
                                <ul class="text-sm text-blue-800 space-y-1">
                                    <li>• Bayar sesuai nominal yang tertera</li>
                                    <li>• Pembayaran akan dikonfirmasi otomatis</li>
                                    <li>• Tiket akan dikirim via email</li>
                                    <li>• Simpan bukti pembayaran</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
<script src="{{ asset('js/payment-monitoring.js') }}"></script>
<script>
// Generate QR Code for QRIS
@if($order->payment_method === 'qris' && isset($paymentData['qr_string']))
    QRCode.toCanvas(document.getElementById('qrcode'), '{{ $paymentData['qr_string'] }}', {
        width: 256,
        height: 256,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    });
@endif

// Generate QR Code for E-Wallet
@if($order->payment_method === 'e_wallet' && isset($paymentData['qr_code']))
    QRCode.toCanvas(document.getElementById('ewallet-qrcode'), '{{ $paymentData['qr_code'] }}', {
        width: 192,
        height: 192,
        margin: 2,
        color: {
            dark: '#000000',
            light: '#FFFFFF'
        }
    });
@endif

// Countdown timer
function startCountdown() {
    const expiresAt = new Date('{{ $order->expires_at }}').getTime();
    const countdownElement = document.getElementById('countdown');

    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = expiresAt - now;

        if (distance < 0) {
            clearInterval(timer);
            countdownElement.innerHTML = "EXPIRED";
            countdownElement.classList.add('text-red-600', 'font-bold');
            window.location.reload();
            return;
        }

        const hours = Math.floor(distance / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        let timeString = '';
        if (hours > 0) {
            timeString = hours + "h " + minutes + "m " + seconds + "s";
        } else {
            timeString = minutes + "m " + seconds + "s";
        }

        countdownElement.innerHTML = timeString;

        if (distance < 5 * 60 * 1000) {
            countdownElement.classList.add('text-red-600', 'animate-pulse');
        } else if (distance < 15 * 60 * 1000) {
            countdownElement.classList.add('text-orange-600');
        }
    }, 1000);
}

// Copy to clipboard
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showNotification('Nomor berhasil disalin!', 'success');
    });
}

// Check payment status
function checkPaymentStatus() {
    fetch('{{ route("orders.check-status", $order) }}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'paid') {
                window.location.href = '{{ route("orders.success", $order) }}';
            } else {
                showNotification('Pembayaran belum diterima. Silakan coba lagi.', 'info');
            }
        })
        .catch(error => {
            showNotification('Gagal mengecek status pembayaran.', 'error');
        });
}

// Initialize payment monitoring
let paymentMonitor;

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

document.addEventListener('DOMContentLoaded', function() {
    startCountdown();

    // Initialize payment monitoring
    if (window.PaymentMonitor) {
        paymentMonitor = new PaymentMonitor();
        paymentMonitor.startMonitoring('{{ $order->id }}');

        // Show initial notification
        showNotification('🔄 Monitoring pembayaran dimulai. Halaman akan otomatis refresh setelah pembayaran berhasil.', 'info');
    }

    // Add payment status indicator
    updatePaymentStatusIndicator();
});

function updatePaymentStatusIndicator() {
    const statusElement = document.querySelector('.payment-status-indicator');
    if (statusElement) {
        statusElement.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                <span class="text-yellow-800">Menunggu pembayaran...</span>
            </div>
        `;
    }
}

// Enhanced check payment status with sound
function checkPaymentStatus() {
    fetch('{{ route("orders.check-status", $order) }}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'paid') {
                // Play success sound
                if (paymentMonitor) {
                    paymentMonitor.playSuccessSound();
                }

                showNotification('🎉 Pembayaran berhasil! Mengalihkan ke halaman tiket...', 'success');

                setTimeout(() => {
                    window.location.href = '{{ route("orders.success", $order) }}';
                }, 2000);
            } else if (data.status === 'failed' || data.status === 'expired') {
                if (paymentMonitor) {
                    paymentMonitor.playErrorSound();
                }
                showNotification('❌ Pembayaran gagal atau kedaluwarsa.', 'error');
            } else {
                showNotification('⏳ Pembayaran belum diterima. Monitoring akan terus berjalan...', 'info');
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
            showNotification('⚠️ Gagal mengecek status pembayaran.', 'error');
        });
}
</script>
@endpush
