<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#A8D5BA">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <?php if(auth()->guard()->check()): ?>
        <meta name="user-authenticated" content="true">
    <?php endif; ?>

    <title><?php echo $__env->yieldContent('title', 'Admin Dashboard'); ?> - <?php echo e(config('app.name', 'TiXara')); ?></title>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=DM+Sans:wght@400;500;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


    <!-- Theme Manager -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/js/theme-manager.js']); ?>

    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body class="font-sans antialiased bg-dynamic-secondary min-h-screen theme-transition">
    <div id="app" class="flex h-screen bg-dynamic-secondary">
        <!-- Sidebar -->
        <aside class="hidden lg:flex lg:flex-shrink-0">
            <div class="flex flex-col w-64">
                <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-dynamic-primary border-r border-dynamic shadow-lg">
                    <!-- Logo -->
                    <div class="flex items-center flex-shrink-0 px-4 mb-8">
                        <div class="flex items-center">
                            <div class="w-10 h-10 theme-bg-primary rounded-xl flex items-center justify-center shadow-md">
                                <span class="text-white font-bold text-lg">T</span>
                            </div>
                            <div class="ml-3">
                                <h1 class="text-xl font-bold text-dynamic-primary">TiXara</h1>
                                <p class="text-sm text-dynamic-secondary">Admin Panel</p>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <nav class="mt-5 flex-1 px-2 space-y-1">
                        <a href="<?php echo e(route('admin.dashboard')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.dashboard') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>
                            Dashboard
                        </a>

                        <a href="<?php echo e(route('admin.tickets.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.tickets.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="calendar" class="mr-3 h-5 w-5"></i>
                            Events Management
                        </a>

                        <a href="<?php echo e(route('admin.users.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.users.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="users" class="mr-3 h-5 w-5"></i>
                            Users
                        </a>

                        <a href="<?php echo e(route('admin.orders.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.orders.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="shopping-cart" class="mr-3 h-5 w-5"></i>
                            Orders
                        </a>

                        <a href="<?php echo e(route('admin.vouchers.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.vouchers.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="tag" class="mr-3 h-5 w-5"></i>
                            Vouchers
                        </a>

                        <a href="<?php echo e(route('admin.categories.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.categories.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="folder" class="mr-3 h-5 w-5"></i>
                            Categories
                        </a>

                        <a href="<?php echo e(route('admin.payments.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.payments.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="credit-card" class="mr-3 h-5 w-5"></i>
                            Payments
                        </a>

                        <a href="<?php echo e(route('admin.notifications.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.notifications.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="bell" class="mr-3 h-5 w-5"></i>
                            Notifications
                        </a>

                        <a href="<?php echo e(route('admin.organizers.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.organizers.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="building" class="mr-3 h-5 w-5"></i>
                            Organizers
                        </a>

                        <a href="<?php echo e(route('admin.settings.index')); ?>"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md <?php echo e(request()->routeIs('admin.settings.*') ? 'theme-bg-primary text-white' : 'text-dynamic-secondary hover:bg-dynamic-secondary hover:text-dynamic-primary'); ?> transition-colors duration-200">
                            <i data-lucide="settings" class="mr-3 h-5 w-5"></i>
                            Settings
                        </a>
                    </nav>

                    <!-- User Info -->
                    <div class="flex-shrink-0 flex border-t border-dynamic p-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 theme-bg-accent rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-semibold">
                                    <?php echo e(substr(auth()->user()->name ?? 'A', 0, 1)); ?>

                                </span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-dynamic-primary"><?php echo e(auth()->user()->name ?? 'Admin'); ?></p>
                                <p class="text-xs text-dynamic-secondary"><?php echo e(ucfirst(auth()->user()->role ?? 'admin')); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex flex-col w-0 flex-1 overflow-hidden">
            <!-- Top Navigation -->
            <div class="relative z-10 flex-shrink-0 flex h-16 bg-dynamic-primary shadow border-b border-dynamic">
                <!-- Mobile menu button -->
                <button type="button"
                        class="px-4 border-r border-dynamic text-dynamic-secondary focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary lg:hidden"
                        x-data=""
                        @click="$dispatch('toggle-sidebar')">
                    <i data-lucide="menu" class="h-6 w-6"></i>
                </button>

                <div class="flex-1 px-4 flex justify-between items-center">
                    <!-- Page Title -->
                    <div class="flex-1">
                        <h1 class="text-2xl font-semibold text-dynamic-primary"><?php echo $__env->yieldContent('title', 'Dashboard'); ?></h1>
                    </div>

                    <!-- Right Side Actions -->
                    <div class="ml-4 flex items-center md:ml-6 space-x-3">
                        <!-- Theme Selector -->
                        <div class="relative" x-data="{ showThemes: false }">
                            <button @click="showThemes = !showThemes"
                                    class="p-2 rounded-lg text-dynamic-secondary hover:text-dynamic-primary hover:bg-dynamic-secondary transition-colors duration-200">
                                <i data-lucide="palette" class="h-5 w-5"></i>
                            </button>

                            <!-- Theme Dropdown -->
                            <div x-show="showThemes"
                                 @click.away="showThemes = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-48 bg-dynamic-primary rounded-lg shadow-lg border border-dynamic py-1 z-50">
                                <div class="px-4 py-2 border-b border-dynamic">
                                    <h3 class="text-sm font-semibold text-dynamic-primary">Pilih Tema</h3>
                                </div>
                                <div class="p-2 space-y-1">
                                    <button onclick="window.changeTheme('green')" class="w-full flex items-center space-x-3 px-3 py-2 rounded hover:bg-dynamic-secondary transition-colors">
                                        <div class="w-4 h-4 rounded-full bg-green-500"></div>
                                        <span class="text-sm text-dynamic-primary">Green</span>
                                    </button>
                                    <button onclick="window.changeTheme('blue')" class="w-full flex items-center space-x-3 px-3 py-2 rounded hover:bg-dynamic-secondary transition-colors">
                                        <div class="w-4 h-4 rounded-full bg-blue-500"></div>
                                        <span class="text-sm text-dynamic-primary">Blue</span>
                                    </button>
                                    <button onclick="window.changeTheme('purple')" class="w-full flex items-center space-x-3 px-3 py-2 rounded hover:bg-dynamic-secondary transition-colors">
                                        <div class="w-4 h-4 rounded-full bg-purple-500"></div>
                                        <span class="text-sm text-dynamic-primary">Purple</span>
                                    </button>
                                    <button onclick="window.changeTheme('pink')" class="w-full flex items-center space-x-3 px-3 py-2 rounded hover:bg-dynamic-secondary transition-colors">
                                        <div class="w-4 h-4 rounded-full bg-pink-500"></div>
                                        <span class="text-sm text-dynamic-primary">Pink</span>
                                    </button>
                                    <button onclick="window.changeTheme('orange')" class="w-full flex items-center space-x-3 px-3 py-2 rounded hover:bg-dynamic-secondary transition-colors">
                                        <div class="w-4 h-4 rounded-full bg-orange-500"></div>
                                        <span class="text-sm text-dynamic-primary">Orange</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Dark Mode Toggle -->
                        <button onclick="window.toggleDarkMode()"
                                class="p-2 rounded-lg text-dynamic-secondary hover:text-dynamic-primary hover:bg-dynamic-secondary transition-colors duration-200">
                            <i data-lucide="moon" class="h-5 w-5 dark:hidden"></i>
                            <i data-lucide="sun" class="h-5 w-5 hidden dark:block"></i>
                        </button>

                        <!-- Notifications -->
                        <div class="relative" x-data="{ showNotifications: false }">
                            <button @click="showNotifications = !showNotifications"
                                    class="relative p-2 rounded-lg text-dynamic-secondary hover:text-dynamic-primary hover:bg-dynamic-secondary transition-colors duration-200">
                                <i data-lucide="bell" class="h-5 w-5"></i>
                                <span class="notification-badge absolute -top-1 -right-1 w-5 h-5 theme-bg-accent text-white text-xs rounded-full flex items-center justify-center" style="display: none;">0</span>
                            </button>

                            <!-- Notifications Dropdown -->
                            <div x-show="showNotifications"
                                 @click.away="showNotifications = false"
                                 x-transition
                                 class="absolute right-0 mt-2 w-80 bg-dynamic-primary rounded-lg shadow-lg border border-dynamic py-2 z-50">
                                <div class="px-4 py-3 border-b border-dynamic flex items-center justify-between">
                                    <h3 class="text-sm font-semibold text-dynamic-primary">Notifications</h3>
                                    <button onclick="markAllNotificationsAsRead()" class="text-xs theme-primary hover:underline">
                                        Mark all read
                                    </button>
                                </div>
                                <div class="notifications-dropdown-content max-h-64 overflow-y-auto">
                                    <div class="p-4 text-center text-dynamic-secondary text-sm">
                                        <i data-lucide="bell" class="w-8 h-8 mx-auto mb-2 text-gray-300"></i>
                                        Loading notifications...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open"
                                    class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <div class="w-8 h-8 theme-bg-primary rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-semibold">
                                        <?php echo e(substr(auth()->user()->name ?? 'A', 0, 1)); ?>

                                    </span>
                                </div>
                            </button>

                            <div x-show="open"
                                 @click.away="open = false"
                                 x-transition
                                 class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-dynamic-primary border border-dynamic">
                                <div class="py-1">
                                    <a href="<?php echo e(route('profile')); ?>"
                                       class="block px-4 py-2 text-sm text-dynamic-primary hover:bg-dynamic-secondary transition-colors">
                                        Profile
                                    </a>
                                    <a href="<?php echo e(route('admin.settings.index')); ?>"
                                       class="block px-4 py-2 text-sm text-dynamic-primary hover:bg-dynamic-secondary transition-colors">
                                        Settings
                                    </a>
                                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit"
                                                class="block w-full text-left px-4 py-2 text-sm text-dynamic-primary hover:bg-dynamic-secondary transition-colors">
                                            Logout
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page Content -->
            <main class="flex-1 relative overflow-y-auto focus:outline-none bg-dynamic-secondary">
                <?php echo $__env->yieldContent('content'); ?>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


    <!-- AOS Init -->
    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 800,
            once: true
        });

        // Initialize Lucide icons
        lucide.createIcons();
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\Project-tixara.my.id\resources\views/layouts/admin.blade.php ENDPATH**/ ?>