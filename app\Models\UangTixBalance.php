<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UangTixBalance extends Model
{
    use HasFactory;

    protected $table = 'uangtix_balances';

    protected $fillable = [
        'user_id',
        'balance',
        'total_earned',
        'total_spent',
        'total_deposited',
        'total_withdrawn',
        'is_active',
    ];

    protected $casts = [
        'balance' => 'decimal:0',
        'total_earned' => 'decimal:0',
        'total_spent' => 'decimal:0',
        'total_deposited' => 'decimal:0',
        'total_withdrawn' => 'decimal:0',
        'is_active' => 'boolean',
    ];

    /**
     * User relationship
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Transactions relationship
     */
    public function transactions(): HasMany
    {
        return $this->hasMany(UangTixTransaction::class, 'user_id', 'user_id');
    }

    /**
     * Check if user can spend amount
     */
    public function canSpend(float $amount): bool
    {
        return $this->is_active && $this->balance >= $amount && $amount > 0;
    }

    /**
     * Add UangTix to balance
     */
    public function addBalance(float $amount, string $type = 'deposit', array $metadata = []): UangTixTransaction
    {
        if ($amount <= 0) {
            throw new \Exception('Amount must be greater than 0');
        }

        $balanceBefore = $this->balance;
        $this->increment('balance', $amount);

        if ($type === 'deposit' || $type === 'admin_add') {
            $this->increment('total_deposited', $amount);
        } elseif ($type === 'transfer_in') {
            $this->increment('total_earned', $amount);
        }

        return $this->createTransaction([
            'type' => $type,
            'amount' => $amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'status' => 'completed',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Deduct UangTix from balance
     */
    public function deductBalance(float $amount, string $type = 'purchase', array $metadata = []): UangTixTransaction
    {
        if (!$this->canSpend($amount)) {
            throw new \Exception('Insufficient UangTix balance');
        }

        $balanceBefore = $this->balance;
        $this->decrement('balance', $amount);

        if ($type === 'purchase') {
            $this->increment('total_spent', $amount);
        } elseif ($type === 'withdrawal' || $type === 'admin_deduct') {
            $this->increment('total_withdrawn', $amount);
        }

        return $this->createTransaction([
            'type' => $type,
            'amount' => -$amount,
            'balance_before' => $balanceBefore,
            'balance_after' => $this->fresh()->balance,
            'status' => 'completed',
            'metadata' => $metadata,
        ]);
    }

    /**
     * Transfer UangTix to another user
     */
    public function transferTo(User $toUser, float $amount, string $description = 'Transfer UangTix'): array
    {
        if (!$this->canSpend($amount)) {
            throw new \Exception('Insufficient UangTix balance');
        }

        $toBalance = $toUser->getUangTixBalance();

        // Create outgoing transaction
        $outTransaction = $this->deductBalance($amount, 'transfer_out', [
            'to_user_id' => $toUser->id,
            'to_user_name' => $toUser->name,
            'description' => $description,
        ]);

        // Create incoming transaction
        $inTransaction = $toBalance->addBalance($amount, 'transfer_in', [
            'from_user_id' => $this->user_id,
            'from_user_name' => $this->user->name,
            'description' => $description,
        ]);

        // Update transaction references
        $outTransaction->update([
            'to_user_id' => $toUser->id,
            'description' => "Transfer ke {$toUser->name}: {$description}",
        ]);

        $inTransaction->update([
            'from_user_id' => $this->user_id,
            'description' => "Transfer dari {$this->user->name}: {$description}",
        ]);

        return [
            'out_transaction' => $outTransaction,
            'in_transaction' => $inTransaction,
        ];
    }

    /**
     * Create transaction record
     */
    private function createTransaction(array $data): UangTixTransaction
    {
        return UangTixTransaction::create(array_merge([
            'transaction_number' => 'UTX-' . strtoupper(uniqid()),
            'user_id' => $this->user_id,
            'description' => $this->getTransactionDescription($data['type'], $data),
        ], $data));
    }

    /**
     * Get transaction description
     */
    private function getTransactionDescription(string $type, array $data): string
    {
        return match($type) {
            'deposit' => 'Deposit UangTix',
            'withdrawal' => 'Penarikan UangTix',
            'purchase' => 'Pembelian tiket',
            'refund' => 'Refund pembelian',
            'admin_add' => 'Penambahan saldo oleh admin',
            'admin_deduct' => 'Pengurangan saldo oleh admin',
            'transfer_in' => 'Transfer masuk',
            'transfer_out' => 'Transfer keluar',
            default => 'Transaksi UangTix'
        };
    }

    /**
     * Get formatted balance
     */
    public function getFormattedBalanceAttribute(): string
    {
        return 'UTX ' . number_format($this->balance, 0, ',', '.');
    }

    /**
     * Get balance in IDR equivalent
     */
    public function getBalanceInIdrAttribute(): float
    {
        $exchangeRate = UangTixExchangeRate::first();
        return $this->balance * ($exchangeRate->rate_uangtix_to_idr ?? 1);
    }

    /**
     * Get formatted balance in IDR
     */
    public function getFormattedBalanceIdrAttribute(): string
    {
        return 'Rp ' . number_format($this->balance_in_idr, 0, ',', '.');
    }

    /**
     * Activate balance
     */
    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    /**
     * Deactivate balance
     */
    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    /**
     * Get recent transactions
     */
    public function getRecentTransactions(int $limit = 10)
    {
        return $this->transactions()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get monthly statistics
     */
    public function getMonthlyStats(int $year = null, int $month = null)
    {
        $year = $year ?? now()->year;
        $month = $month ?? now()->month;

        return $this->transactions()
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->selectRaw('
                type,
                COUNT(*) as count,
                SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_in,
                SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as total_out
            ')
            ->groupBy('type')
            ->get();
    }
}
